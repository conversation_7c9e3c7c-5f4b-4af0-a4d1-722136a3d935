from sqlalchemy import Column, Integer, String, DateTime, Text, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)
    company = Column(String(255), nullable=False)
    location = Column(String(255), nullable=False)

    posting_date = Column(DateTime, nullable=True)          # parsed datetime
    posting_date_raw = Column(String(128), nullable=True)   # e.g. "2 days ago"
    job_type = Column(String(64), nullable=True)            # e.g. Full-time
    tags = Column(Text, nullable=True)                      # comma-separated tags

    created_at = Column(DateTime, server_default=func.now())       # auto now
    updated_at = Column(DateTime, server_default=func.now(), 
                        onupdate=func.now())                       # auto update
