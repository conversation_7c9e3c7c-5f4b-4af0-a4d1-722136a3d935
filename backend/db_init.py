import os
from sqlalchemy import create_engine
from sqlalchemy.exc import SQLAlchemyError
from models import Base
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:@localhost/jobboard")

try:
    engine = create_engine(DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    print("✅ Tables created successfully!")
except ModuleNotFoundError as e:
    print("❌ Missing required package:", e)
    print("Try running: pip install pymysql")
except SQLAlchemyError as e:
    print("❌ SQLAlchemy error:", e)
except Exception as e:
    print("❌ Unexpected error:", e)